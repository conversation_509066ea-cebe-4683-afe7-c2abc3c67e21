<?php

namespace Tests\Feature;

use App\Booking;
use App\Listing;
use App\User;
use App\Services\BookingService;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class BookingTodayStatusTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $serviceUser;
    protected $customerUser;
    protected $listing;
    protected $bookingService;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create roles
        Role::create(['name' => 'service']);
        Role::create(['name' => 'customer']);
        Role::create(['name' => 'user']);
        
        // Create users
        $this->serviceUser = User::factory()->create();
        $this->serviceUser->assignRole('service');
        
        $this->customerUser = User::factory()->create();
        $this->customerUser->assignRole('customer');
        
        // Create a listing
        $this->listing = Listing::factory()->create([
            'user_id' => $this->serviceUser->id,
        ]);

        $this->bookingService = new BookingService();
    }

    /** @test */
    public function it_includes_ongoing_bookings_in_today_status()
    {
        // Create a booking that started yesterday and ends tomorrow (ongoing today)
        $yesterday = Carbon::yesterday()->toDateString();
        $tomorrow = Carbon::tomorrow()->toDateString();
        
        $booking = Booking::factory()->create([
            'user_id' => $this->customerUser->id,
            'provider_id' => $this->serviceUser->id,
            'listing_id' => $this->listing->id,
            'check_in' => $yesterday,
            'check_out' => $tomorrow,
            'status' => 0, // pending
        ]);

        $this->actingAs($this->serviceUser);
        
        $request = new Request(['status' => 'today']);
        $result = $this->bookingService->getBookingsWithFilters($request);
        
        $this->assertCount(1, $result);
        $this->assertEquals($booking->id, $result->first()->id);
    }

    /** @test */
    public function it_includes_bookings_starting_today_in_today_status()
    {
        // Create a booking that starts today
        $today = Carbon::today()->toDateString();
        $tomorrow = Carbon::tomorrow()->toDateString();
        
        $booking = Booking::factory()->create([
            'user_id' => $this->customerUser->id,
            'provider_id' => $this->serviceUser->id,
            'listing_id' => $this->listing->id,
            'check_in' => $today,
            'check_out' => $tomorrow,
            'status' => 0, // pending
        ]);

        $this->actingAs($this->serviceUser);
        
        $request = new Request(['status' => 'today']);
        $result = $this->bookingService->getBookingsWithFilters($request);
        
        $this->assertCount(1, $result);
        $this->assertEquals($booking->id, $result->first()->id);
    }

    /** @test */
    public function it_excludes_future_bookings_from_today_status()
    {
        // Create a booking that starts tomorrow
        $tomorrow = Carbon::tomorrow()->toDateString();
        $dayAfterTomorrow = Carbon::tomorrow()->addDay()->toDateString();
        
        $booking = Booking::factory()->create([
            'user_id' => $this->customerUser->id,
            'provider_id' => $this->serviceUser->id,
            'listing_id' => $this->listing->id,
            'check_in' => $tomorrow,
            'check_out' => $dayAfterTomorrow,
            'status' => 0, // pending
        ]);

        $this->actingAs($this->serviceUser);
        
        $request = new Request(['status' => 'today']);
        $result = $this->bookingService->getBookingsWithFilters($request);
        
        $this->assertCount(0, $result);
    }

    /** @test */
    public function it_excludes_past_bookings_from_today_status()
    {
        // Create a booking that ended yesterday
        $twoDaysAgo = Carbon::today()->subDays(2)->toDateString();
        $yesterday = Carbon::yesterday()->toDateString();
        
        $booking = Booking::factory()->create([
            'user_id' => $this->customerUser->id,
            'provider_id' => $this->serviceUser->id,
            'listing_id' => $this->listing->id,
            'check_in' => $twoDaysAgo,
            'check_out' => $yesterday,
            'status' => 0, // pending
        ]);

        $this->actingAs($this->serviceUser);
        
        $request = new Request(['status' => 'today']);
        $result = $this->bookingService->getBookingsWithFilters($request);
        
        $this->assertCount(0, $result);
    }

    /** @test */
    public function it_sorts_today_bookings_by_status_priority()
    {
        $today = Carbon::today()->toDateString();
        $yesterday = Carbon::yesterday()->toDateString();
        $tomorrow = Carbon::tomorrow()->toDateString();

        // Create an ongoing booking (started yesterday, ends tomorrow)
        $ongoingBooking = Booking::factory()->create([
            'user_id' => $this->customerUser->id,
            'provider_id' => $this->serviceUser->id,
            'listing_id' => $this->listing->id,
            'check_in' => $yesterday,
            'check_out' => $tomorrow,
            'status' => 0, // pending
            'booking_number' => 'BK001',
        ]);

        // Create an upcoming booking (starts today)
        $upcomingBooking = Booking::factory()->create([
            'user_id' => $this->customerUser->id,
            'provider_id' => $this->serviceUser->id,
            'listing_id' => $this->listing->id,
            'check_in' => $today,
            'check_out' => $tomorrow,
            'status' => 0, // pending
            'booking_number' => 'BK002',
        ]);

        // Create a completed booking
        $completedBooking = Booking::factory()->create([
            'user_id' => $this->customerUser->id,
            'provider_id' => $this->serviceUser->id,
            'listing_id' => $this->listing->id,
            'check_in' => $today,
            'check_out' => $today,
            'status' => 3, // completed
            'booking_number' => 'BK003',
        ]);

        $this->actingAs($this->serviceUser);
        
        $request = new Request(['status' => 'today']);
        $result = $this->bookingService->getBookingsWithFilters($request);
        
        $this->assertCount(3, $result);
        
        // The sorting should prioritize: Ongoing -> Upcoming -> Completed
        // Note: The exact order depends on the complex SQL sorting logic in applySorting method
        // We're mainly testing that all relevant bookings are included
        $bookingIds = $result->pluck('id')->toArray();
        $this->assertContains($ongoingBooking->id, $bookingIds);
        $this->assertContains($upcomingBooking->id, $bookingIds);
        $this->assertContains($completedBooking->id, $bookingIds);
    }
}
