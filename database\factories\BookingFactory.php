<?php

namespace Database\Factories;

use App\Booking;
use App\Listing;
use App\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class BookingFactory extends Factory
{
    protected $model = Booking::class;

    public function definition()
    {
        return [
            'user_id' => User::factory(),
            'provider_id' => User::factory(),
            'listing_id' => Listing::factory(),
            'booking_number' => 'BK' . $this->faker->unique()->numberBetween(100000, 999999),
            'check_in' => $this->faker->dateTimeBetween('now', '+1 week'),
            'check_out' => $this->faker->dateTimeBetween('+1 week', '+2 weeks'),
            'guest' => $this->faker->numberBetween(1, 4),
            'listing_basis' => $this->faker->randomElement(['Daily', 'Hourly']),
            'listing_price' => $this->faker->numberBetween(50, 500),
            'total_amount' => $this->faker->numberBetween(100, 1000),
            'total_usd_amount' => $this->faker->numberBetween(25, 250),
            'sub_total' => $this->faker->numberBetween(90, 900),
            'status' => 0, // pending by default
            'payment_method' => $this->faker->randomElement(['stripe', 'paypal']),
            'total_days' => $this->faker->numberBetween(1, 7),
        ];
    }

    public function pending()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 0,
            ];
        });
    }

    public function confirmed()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 1,
            ];
        });
    }

    public function completed()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 3,
            ];
        });
    }

    public function cancelled()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 7,
            ];
        });
    }

    public function daily()
    {
        return $this->state(function (array $attributes) {
            return [
                'listing_basis' => 'Daily',
            ];
        });
    }

    public function hourly()
    {
        return $this->state(function (array $attributes) {
            return [
                'listing_basis' => 'Hourly',
            ];
        });
    }
}
