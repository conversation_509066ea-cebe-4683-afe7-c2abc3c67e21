<?php

namespace Database\Factories;

use App\Listing;
use App\User;
use App\Category;
use Illuminate\Database\Eloquent\Factories\Factory;

class ListingFactory extends Factory
{
    protected $model = Listing::class;

    public function definition()
    {
        return [
            'user_id' => User::factory(),
            'category_id' => 1, // Assuming category with ID 1 exists
            'name' => $this->faker->sentence(3),
            'slug' => $this->faker->slug,
            'description' => $this->faker->paragraph,
            'address' => $this->faker->address,
            'city' => $this->faker->city,
            'state' => $this->faker->state,
            'country' => $this->faker->country,
            'zipcode' => $this->faker->postcode,
            'latitude' => $this->faker->latitude,
            'longitude' => $this->faker->longitude,
            'price' => $this->faker->numberBetween(50, 500),
            'guest' => $this->faker->numberBetween(1, 8),
            'bedroom' => $this->faker->numberBetween(1, 5),
            'bathroom' => $this->faker->numberBetween(1, 3),
            'property_type' => $this->faker->randomElement(['Apartment', 'House', 'Villa', 'Condo']),
            'room_type' => $this->faker->randomElement(['Entire place', 'Private room', 'Shared room']),
            'bed' => $this->faker->numberBetween(1, 6),
            'listing_basis' => $this->faker->randomElement(['Daily', 'Hourly']),
            'status' => 1, // active by default
            'is_verified' => 1,
            'is_featured' => 0,
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    public function active()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 1,
            ];
        });
    }

    public function inactive()
    {
        return $this->state(function (array $attributes) {
            return [
                'status' => 0,
            ];
        });
    }

    public function daily()
    {
        return $this->state(function (array $attributes) {
            return [
                'listing_basis' => 'Daily',
            ];
        });
    }

    public function hourly()
    {
        return $this->state(function (array $attributes) {
            return [
                'listing_basis' => 'Hourly',
            ];
        });
    }

    public function verified()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_verified' => 1,
            ];
        });
    }

    public function featured()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_featured' => 1,
            ];
        });
    }
}
